import Element from "@/app/core/element";
import Entry from "@/app/core/entry";
import Target from "@/app/core/target";
import cypressHelper from "@/app/helpers/cypress";
import h from "@/app/helpers/all";
import path from "path";

export default {
  currentWindowUrl: "",

  // ****************************************************************
  // Page Elements Actions
  // ****************************************************************
  Check: function () {
    if (
      Cypress.sdt.current.step.simpleValues?.find(
        (value) =>
          h.compareNormalizedStrings(value, "notexist") ||
          h.compareNormalizedStrings(value, "notexists")
      )
    ) {
      const selector = Cypress.sdt.current.step.target.lastElement.selector;
      const content = Cypress.sdt.current.step.target.lastElement.content;
      return cy
        .contains(`${selector}:contains(${content})`)
        .should(($elem: JQuery<HTMLElement>) => {
          if (!$elem || $elem.length === 0) return expect($elem).to.not.exist;
          else return expect($elem).to.be.hidden;
        });
    }

    let validation = standardValidation;
    if (
      Cypress.sdt.current.step.target?.lastElementNameIs(
        "Input With Label Error"
      )
    ) {
      validation = ($elem) => {
        const $formField = $elem.closest("mat-form-field");
        const firstError = $formField.find("mat-error")[0];
        const secondError = $formField.find("mat-error")[1];
        let errorText = firstError ? firstError.innerText.trim() : "";
        errorText = secondError ? secondError.innerText.trim() : errorText;
        expect(errorText).to.equal(Cypress.sdt.current.step.simpleValues[0]);
      };
    } else if (
      Cypress.sdt.current.step.target?.lastElementNameIs("Table Row")
    ) {
      validation = ($elem) => {
        const itemsNamedValue = Cypress.sdt.current.step.namedValues.find(
          (namedValue) => namedValue.left.includes("items")
        );
        const itemsNumber = itemsNamedValue.right.match(/(\d+)/);
        expect($elem.length).to.equal(parseInt(itemsNumber));
      };
    }

    Cypress.sdt.current.step.target.get({ validation });
  },
  "Check Mouse Pointer": function () {
    const shape = Cypress.sdt.current.step.simpleValues[0] ?? "pointer";
    Cypress.sdt.current.step.target
      .get()
      .realHover("mouse")
      .should("have.css", "cursor", shape);
  },
  "Check Tooltip": function () {
    const tooltipText = Cypress.sdt.current.step.simpleValues[0];
    Cypress.sdt.current.step.target.get().realHover();
    const element = new Element({ label: "{Tooltip}" });
    const selector = element["selector"];
    cy.contains(selector, tooltipText);
  },
  "Check Url": function () {
    let url = Cypress.sdt.current.step.simpleValues[0];
    if (!url.startsWith("http")) url = Cypress.config("baseUrl") + url;
    cy.url().should((currentUrl) => expect(currentUrl).to.include(url));
  },
  Clear: function () {
    Cypress.sdt.current.step.target.get().clear({ force: true });
  },
  Click: function () {
    const currentStep = Cypress.sdt.current.step;
    const targetObject = Cypress.sdt.current.step.target;
    const config = Cypress.sdt.config;

    // Force click for specific elements
    const forceClickElements = [
      {
        condition: () =>
          targetObject.label?.startsWith(config.recorderFileSelector),
        type: "file selector",
      },
      { condition: () => targetObject.lastElementNameIs("icon"), type: "icon" },
      { condition: () => targetObject.lastElementNameIs("tab"), type: "tab" },
      {
        condition: () => targetObject.lastElementNameIncludes("input"),
        type: "input",
      },
    ];

    if (forceClickElements.some((elem) => elem.condition())) {
      targetObject.get().click({ force: true });
      return;
    }

    // Handle body click
    if (targetObject.lastElementNameIs("body")) {
      cy.get("body").click();
      return;
    }

    // Handle links with cross-origin navigation
    if (targetObject.lastElementNameIs("link")) {
      const avoidSameOrigin =
        currentStep.simpleValues[0]?.toLowerCase() ===
        "avoid same origin policy";

      if (avoidSameOrigin) {
        targetObject
          .get()
          .invoke("attr", "href")
          .then((href) => {
            cy.origin(href, () => cy.visit(href));
          });
        return;
      }
    }

    // Default click behavior
    targetObject.get().click();
  },
  Focus: function () {
    if (Cypress.sdt.current.step.target.lastElementNameIs("checkbox")) {
      Cypress.sdt.current.step.target
        .get()
        .invoke("attr", "aria-checked")
        .should("eq", "true");
    }
    Cypress.sdt.current.step.target.get().focus();
  },
  Hover: function () {
    Cypress.sdt.current.step.target.get().realHover();
  },
  "Press Key": function () {
    const keyToType = Cypress.sdt.current.step.simpleValues[0]
      .toLowerCase()
      .trim()
      .replace(/\s*/g, "");
    if (Cypress.sdt.current.step.target.label) {
      Cypress.sdt.current.step.target.get().focus();
    }
    if (keyToType.toLowerCase().trim() === "{tab}") {
      cy.focused().tab();
    } else if (keyToType.toLowerCase().trim() === "{shiftTab}") {
      cy.focused().tab({ shift: true });
    } else cy.focused().type(`{${keyToType}}`);
  },
  Select: function () {
    if (Cypress.sdt.current.step.target.lastElementNameIs("Menu Option")) {
      const menuOptions = Cypress.sdt.current.step.simpleValues;
      menuOptions.forEach((menuOption, index) => {
        if (index === 0) {
          cy.get(`mat-sidenav a:contains(${menuOption})`).click();
        } else {
          cy.get(`button:contains(${menuOption})`).click();
        }
      });
      return;
    }
    Cypress.sdt.current.step.target.get().click();
    const element = new Element({ label: "{Not Visible Option}" });
    const selector = element["selector"];
    const option = Cypress.sdt.current.step.simpleValues[0];
    cy.get(`${selector}`)
      .filter((_, el) => {
        return Cypress.$(el).text().trim() === option;
      })
      .click();
  },
  "Set Checkbox": function () {
    Cypress.sdt.current.step.target.get().then((checkbox) =>
      cy
        .get(checkbox)
        .invoke("prop", "checked")
        .then((checkedProp) => {
          if (
            checkedProp === false &&
            Cypress.sdt.current.step.simpleValues[0].toLowerCase().trim() ===
              "checked"
          ) {
            cy.get(checkbox).click();
          }
          if (
            checkedProp === true &&
            (!Cypress.sdt.current.step.simpleValues[0] ||
              Cypress.sdt.current.step.simpleValues[0].toLowerCase().trim() ===
                "unchecked")
          ) {
            cy.get(checkbox).click();
          }
        })
    );
  },
  Type: function () {
    let valueToType = Cypress.sdt.current.step.simpleValues[0];
    if (!valueToType) {
      const entry = new Entry(
        `[${Cypress.sdt.current.step.target.lastElement.content}]`
      );
      valueToType = entry.expandEntry();
    }
    Cypress.sdt.current.step.target
      .get()
      .clear({ force: true })
      .type(valueToType);
  },
  // ****************************************************************
  // Whole Page Actions
  // ****************************************************************
  "Bypass New Browser Tab": function () {
    cy.window().then((win) => {
      cy.stub(win, "open").as("OpenBrowserTab");
    });
  },
  "Bypass New Browser Window": function () {
    cy.window().then((win) => {
      Cypress.sdt.currentWindowUrl = win.location.href;
      cy.stub(win, "open").as("OpenBrowserWindow");
    });
  },
  "Check New Browser Tab": function () {
    const newBrowserTabUrl = Cypress.sdt.current.step.simpleValues[0];
    cy.get("@OpenBrowserTab")
      .should(
        "have.been.calledOnceWith",
        Cypress.sinon.match(new RegExp(newBrowserTabUrl))
      )
      .invoke("restore");
  },
  "Check New Browser Window": function () {
    const newBrowserWindowUrl = Cypress.sdt.current.step.simpleValues[0];
    cy.get("OpenBrowserWindow")
      .should(
        "have.been.calledOnceWith",
        Cypress.sinon.match(new RegExp(newBrowserWindowUrl))
      )
      .invoke("restore");
    cy.window().then((win) => {
      win.location.href = Cypress.sdt.currentWindowUrl;
    });
  },
  Go: function () {
    const direction = Cypress.sdt.current.step.simpleValues[0];
    if (["back", "forward"].includes(direction.toLowerCase().trim())) {
      cy.go(direction.toLowerCase().trim());
    }
  },
  "Goto URL": function () {
    let url = Cypress.sdt.current.step.simpleValues[0];
    if (!url.startsWith("http")) url = `${Cypress.config("baseUrl")}/${url}`;
    cy.visit(url);
  },
  "Open Page": function () {
    let url =
      Cypress.sdt.current.step.simpleValues[0] ?? Cypress.config("baseUrl");
    const waitInterval = Cypress.sdt.current.step.simpleValues[1] ?? 2;
    if (!url.startsWith("http")) url = `${Cypress.config("baseUrl")}/${url}`;
    if (waitInterval) {
      cy.visit(url).wait(waitInterval * 1000);
    } else cy.visit(url);
  },
  Scroll: function () {
    let targetObject = Cypress.sdt.current.step.target;
    if (!targetObject.label) {
      targetObject = new Target("form");
    }
    const position = !Cypress.sdt.current.step.simpleValues[0]
      ? "bottom"
      : Cypress.sdt.current.step.simpleValues[0].toLowerCase();

    targetObject
      .get()
      .scrollTo(position, { ensureScrollable: false, timeout: 4000 });
  },
  "Scroll Into View": function () {
    const lastElement =
      Cypress.sdt.current.step.target.elements[
        Cypress.sdt.current.step.target.elements.length - 1
      ];
    if (lastElement) {
      lastElement.isVisible = false;
      lastElement.selector = lastElement.selector.replace(":visible", "");
    }
    Cypress.sdt.current.step.target.get().scrollIntoView();
  },
  // ****************************************************************
  // Auxiliary Actions
  // ****************************************************************
  Log: function () {
    if (Cypress.sdt.current.step.simpleValues[0])
      cy.log(Cypress.sdt.current.step.simpleValues[0]);
    if (Cypress.sdt.current.step.simpleValues[1])
      cy.log(Cypress.sdt.current.step.simpleValues[1]);
  },
  LogToConsole: function () {
    cypressHelper.clog("===================");
    cypressHelper.clog(Cypress.sdt.current.step.label);
    cypressHelper.clog("-----");
    if (Cypress.sdt.current.step.simpleValues[0])
      cypressHelper.clog(Cypress.sdt.current.step.simpleValues[0]);
    if (Cypress.sdt.current.step.simpleValues[1])
      cypressHelper.clog(Cypress.sdt.current.step.simpleValues[1]);
    cypressHelper.clog("===================");
  },
  Pause: function () {
    cy.pause();
  },
  "Run Node Task": function () {
    const taskToRun = Cypress.sdt.current.step.simpleValues[0]
      .toLowerCase()
      .trim();
    cy.task(taskToRun);
  },
  "Set Interceptor": function () {
    const interceptor =
      Cypress.sdt.apiInterceptors[Cypress.sdt.current.step.simpleValues[0]];
    cy.intercept(interceptor.httpMethod, interceptor.endPoint).as("waitForApi");
  },
  "Set New Date": function () {
    const newDate = Cypress.sdt.current.step.simpleValues[0];
    cy.clock(new Date(newDate));
  },
  "Set Original Date": function () {
    window.Date = Cypress.sdt.OriginalDate;
  },
  Wait: function () {
    if (Cypress.sdt.current.step.target?.lastElementNameIs("spinner")) {
      const spinnerSelector =
        Cypress.sdt.current.step.target?.lastElement?.selector;
      cy.wait(1000).then(() => {
        if (Cypress.$(spinnerSelector).length > 0) {
          cy.get(spinnerSelector).should("exist");
          cy.get(spinnerSelector).should("have.length", 0);
        }
      });
    }
    let timeout = 2000;
    if (Cypress.sdt.current.step.simpleValues[0])
      timeout = parseInt(Cypress.sdt.current.step.simpleValues[0]) * 1000;
    cy.wait(timeout);
  },
  "Wait Interceptor": (_sdt) => {
    cy.wait("@waitForApi").its("response.statusCode").should("eq", 200);
  },
  // ****************************************************************
  // DB Actions
  // ****************************************************************
  "Drop DB": function () {
    cy.task("dbDropDatabase");
  },
  "Export DB": function (folderName) {
    folderName = folderName || Cypress.sdt.current.step?.simpleValues[0];
    const folderPath = path.join(
      Cypress.sdt.config.runFolder,
      "dbJsonFiles",
      folderName
    );
    cy.task("exportDb", folderPath);
  },
  "Set DB": function (folderName) {
    folderName = folderName || Cypress.sdt.current.step?.simpleValues[0];
    const folderPath = path.join(
      Cypress.sdt.config.runFolder,
      "dbJsonFiles",
      folderName
    );
    cy.task("setDb", folderPath);
  },
  // ****************************************************************
  // Entity Actions
  // ****************************************************************
  "Check Current Entity": function () {
    const expectedCurrentEntity = Cypress.sdt.current.step.simpleValues[0];
    if (
      !expectedCurrentEntity ||
      expectedCurrentEntity.toLowerCase().trim() === "none"
    ) {
      expect(Cypress.sdt.domain.getCurrentEntityId()).to.be.null;
    } else {
      expect(Cypress.sdt.domain.getCurrentEntityId()).to.equal(
        expectedCurrentEntity
      );
    }
  },
  "Check Entity Field": function () {
    if (Cypress.sdt.current.step.target) {
      const entityFieldPath = Cypress.sdt.current.step.target;
      const entityFieldValue =
        Cypress.sdt.domain.getEntityFieldValue(entityFieldPath);
      expect(entityFieldValue).to.equal(
        Cypress.sdt.current.step.simpleValues[0]
      );
      return;
    }
    Cypress.sdt.current.step.namedValues.forEach((namedValue) => {
      const entityFieldPath = namedValue.left;
      const entityFieldValue =
        Cypress.sdt.domain.getEntityFieldValue(entityFieldPath);
      const value = namedValue.right;
      expect(entityFieldValue).to.equal(value);
    });
  },
  "Reset Current Entity": function () {
    Cypress.sdt.domain.resetCurrentEntityId();
  },
  "Set Current Entity": function () {
    Cypress.sdt.domain.setCurrentEntity(
      Cypress.sdt.current.step.simpleValues[0]
    );
  },
  "Set Entity": function () {
    if (Cypress.sdt.current.step.target) {
      Cypress.sdt.domain.setEntity(
        Cypress.sdt.current.step.target,
        Cypress.sdt.current.step.simpleValues[0]
      );
      return;
    }
    Cypress.sdt.current.step.simpleValues?.forEach((value) => {
      if (h.isObject(value)) {
        Cypress.sdt.domain.setEntity(value["Id"], value);
        return;
      }
      if (typeof value == "string" && value.includes("=")) {
        const entityFieldPath = value.split("=")[0].trim();
        const entityFieldValue = value.split("=")[1].trim();
        Cypress.sdt.domain.setEntity(entityFieldPath, entityFieldValue);
        return;
      }
      if (typeof value == "string") {
        Cypress.sdt.domain.setEntity(value, {});
        return;
      }
    });
  },
};

function standardValidation($elem) {
  Cypress.sdt.current.step.simpleValues?.forEach((value) => {
    checkAssertion($elem, value);
  });

  Cypress.sdt.current.step.namedValues?.forEach((namedValue) => {
    const key = namedValue.left;
    let value = namedValue.right;

    if (key === "attribute") {
      checkAssertion($elem, value);
      return;
    }

    if (key.startsWith("attr-")) {
      const attributeName = key.replace(/attr-/, "").toLowerCase().trim();
      checkProperty($elem, attributeName, value);
      return;
    }

    const propertyName = key.replace(/prop-/, "").toLowerCase().trim();
    value = value.replace(/['"]/g, "");
    if (propertyName) {
      checkProperty($elem, propertyName, value);
    }
  });
}

function checkAssertion($elem, assertion) {
  switch (h.normalizeString(assertion)) {
    case "exist":
    case "exists":
      return expect($elem).to.exist;
    case "hidden":
      return expect($elem).to.be.hidden;
    case "checked":
      return expect($elem).to.be.checked;
    case "unchecked":
    case "notchecked":
      return expect($elem).to.be.not.checked;
    case "empty":
      return expect($elem).to.have.value("");
    case "notempty":
      return expect($elem).to.not.have.value("");
    case "enabled":
      return expect($elem).to.satisfy(($el: any) => {
        return $el.is(":enabled") || $el.attr("aria-disabled") === "false";
      });
    case "disabled":
      return expect($elem).to.satisfy(($el: any) => {
        return $el.is(":disabled") || $el.attr("aria-disabled") === "true";
      });
    case "readonly":
      return expect($elem).to.have.attr("readonly");
    case "required":
      return expect($elem).to.have.attr("required");
    case "notrequired":
    case "optional":
      return expect($elem).to.not.have.attr("required");
    case "bold":
      return expect($elem).to.have.css("font-weight", "700");
    case "italic":
      return expect($elem).to.have.css("font-style", "italic");
    case "underline":
      return expect($elem).to.have.css("Text-decoration", "underline");
    case "blink":
      return expect($elem).to.have.css("Text-decoration", "blink");
  }

  assertion = assertion.trim();
  if (Cypress.sdt.current.step.icon?.left)
    assertion = `${Cypress.sdt.current.step.icon.left.trim()} ${assertion}`;
  if (Cypress.sdt.current.step.icon?.right)
    assertion = `${assertion} ${Cypress.sdt.current.step.icon.right.trim()}`;

  let elementText = $elem[0].innerText ? $elem[0].innerText : $elem.val();
  elementText = elementText.toString().trim();
  if (Cypress.sdt.current.step.target.isPartial)
    expect(elementText).to.include(assertion);
  else expect(elementText).to.be.equal(assertion);
}

function checkProperty($elem, property, value) {
  if (property === "color") {
    let color;
    if (value.startsWith("rgb")) {
      color = value;
    } else color = h.colorCode[value].toLowerCase().trim();
    return expect($elem).to.have.css("Color", color);
  }

  if (property === "text") {
    if (Cypress.sdt.current.step.target.isPartial)
      return expect(($elem.val() || $elem.text()).toString().trim()).to.include(
        value
      );
    if (Cypress.sdt.current.step.icon?.left)
      value = Cypress.sdt.current.step.icon.left + value;
    if (Cypress.sdt.current.step.icon?.right)
      value = value + Cypress.sdt.current.step.icon.right;
    return expect(($elem.val() || $elem.text()).toString().trim()).to.equal(
      value
    );
  }

  if (property === "direction") {
    if (h.compareNormalizedStrings(value, "row")) {
      return expect($elem).to.have.class("flex-row");
    } else if (h.compareNormalizedStrings(value, "column")) {
      return expect($elem).to.have.class("flex-column");
    }
  }

  if (property === "href") {
    return expect($elem).to.have.attr("href", value);
  }

  if (property === "items") {
    return expect($elem).to.have.length(value);
  }

  if (property === "count") {
    return expect($elem).to.have.length(value);
  }

  value =
    typeof value == "string" && value.toLowerCase() === "true"
      ? (value = true)
      : value;
  value =
    typeof value == "string" && value.toLowerCase() === "false"
      ? (value = false)
      : value;

  return expect($elem).to.have.property(property, value);
}
