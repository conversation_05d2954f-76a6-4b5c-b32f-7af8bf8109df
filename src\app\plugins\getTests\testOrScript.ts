import h from "@/app/helpers/all";

let currentSheet;
let rowIndex;
let currentBlockType;

export default function getSheetTestsOrScripts(
  sheetName,
  sheetContent,
  blockType
) {
  currentSheet = sheetContent;
  currentBlockType = blockType;
  rowIndex = 0;
  let previousRowIndex;
  const sheetTestsOrScripts = [];

  while (rowIndex < currentSheet.length) {
    previousRowIndex = rowIndex;
    const sheetRow = currentSheet[rowIndex];
    let blockFirstRow;
    if (currentBlockType === "test")
      blockFirstRow = checkConsecutiveElements(sheetRow, "run");
    if (currentBlockType === "script")
      blockFirstRow = checkConsecutiveElements(sheetRow, "script");
    if (blockFirstRow >= 0) {
      const testEntry = getTestOrScript();
      if (testEntry) {
        testEntry["sheet"] = sheetName;
        if (testEntry["run"]) {
          testEntry["runFlag"] = testEntry["run"].toLowerCase().trim();
          delete testEntry["run"];
        }
        sheetTestsOrScripts.push(testEntry);
      }
    }
    rowIndex === previousRowIndex ? rowIndex++ : rowIndex;
  }

  return sheetTestsOrScripts;
}

function checkConsecutiveElements(arr, value) {
  for (let i = 0; i < arr?.length - 1; i++) {
    if (
      arr[i]?.toString().toLowerCase().trim() === value.toLowerCase().trim() &&
      arr[i + 1]?.toString().toLowerCase().trim() === value.toLowerCase().trim()
    ) {
      return i;
    }
  }
  return -1;
}

function getTestOrScript() {
  let header;
  let steps;
  while (rowIndex < currentSheet.length) {
    if (inHeader()) {
      header = getHeader();
    } else if (inSteps()) {
      steps = getSteps();
      return { ...header, steps: steps };
    }
    rowIndex++;
  }
}

function getHeader() {
  const header = {};
  while (inHeader()) {
    const cellValue = h.toCamelCase(currentSheet[rowIndex]?.[0]);
    if (cellValue) {
      header[cellValue] = currentSheet[rowIndex]?.[2];
    }
    rowIndex++;
  }
  return header;
}

function getSteps() {
  const steps = [];
  let stopFlag;

  while (rowIndex < currentSheet.length && inSteps()) {
    const rowNumber = currentSheet[rowIndex].slice(-1).toString();
    const cellValue = currentSheet[rowIndex]?.[0]?.toLowerCase().trim();
    const nextCellValue = currentSheet[rowIndex]?.[1]?.toLowerCase().trim();
    if (!stopFlag) {
      if (
        cellValue === "x" ||
        cellValue === "z" ||
        (!cellValue && nextCellValue)
      ) {
        const step = getStep(currentSheet[rowIndex].slice(0));
        steps.push({ ...step, rowNumber });
      } else if (cellValue && cellValue !== "do" && cellValue !== "run") {
        if (cellValue.startsWith("=")) {
          steps.push({
            title: currentSheet[rowIndex][0].trim(),
            rowNumber,
          });
        } else {
          steps.push({
            subTitle: currentSheet[rowIndex][0].trim(),
            rowNumber,
          });
        }
      }
    }

    if (cellValue === "z") {
      stopFlag = true;
    }

    rowIndex++;
  }

  return steps;
}

function inHeader() {
  const cellValue = currentSheet[rowIndex]?.[0];
  return (
    cellValue &&
    cellValue === currentSheet[rowIndex][1] &&
    cellValue !== currentSheet[rowIndex][2]
  );
}

function inSteps() {
  const cellValue = currentSheet[rowIndex]?.[0];
  const nextCellValue = currentSheet[rowIndex]?.[1];
  return (cellValue || nextCellValue) && !inHeader();
}

function getStep(rowData) {
  const stepFields = [
    "do",
    "action",
    "target",
    "values",
    "screenshot",
    "notes",
  ];
  const parsedStep = stepFields.reduce((step, field, index) => {
    if (field === "do") {
      if (rowData[index]) {
        step[field] = rowData[index].toLowerCase().trim();
      } else step[field] = "";
    } else if (rowData[index] && typeof rowData[index] === "string") {
      step[field] = rowData[index].trim();
    }
    return step;
  }, {});
  return parsedStep;
}
