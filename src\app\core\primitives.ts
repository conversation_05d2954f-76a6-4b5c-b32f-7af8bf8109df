export default {
  Date: function (period = "1", unit = "d", format = "MM/DD/YYYY") {
    const date = new Date();
    const value = parseInt(period.trim(), 10);
    switch (unit.trim()) {
      case "d":
        date.setDate(date.getDate() + value);
        break;
      case "wd": // Add working days (skip weekends)
        let added = 0;
        while (added < value) {
          date.setDate(date.getDate() + 1);
          const dayOfWeek = date.getDay();
          if (dayOfWeek !== 0 && dayOfWeek !== 6) {
            // 0 = Sunday, 6 = Saturday
            added++;
          }
        }
        break;
      case "w":
        date.setDate(date.getDate() + value * 7);
        break;
      case "m":
        date.setMonth(date.getMonth() + value);
        break;
      case "y":
        date.setFullYear(date.getFullYear() + value);
        break;
    }
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const year = String(date.getFullYear());
    return format.trim().replace(/(yyyy|YYYY|yy|YY|mm|MM|m|M|dd|DD|d|D)/g, (match) => {
      switch (match) {
        case "yyyy":
        case "YYYY":
          return year;
        case "yy":
        case "YY":
          return year.slice(-2);
        case "mm":
        case "MM":
          return month;
        case "m":
        case "M":
          return String(date.getMonth() + 1);
        case "dd":
        case "DD":
          return day;
        case "d":
        case "D":
          return String(date.getDate());
        default:
          return match;
      }
    });
  },
};
