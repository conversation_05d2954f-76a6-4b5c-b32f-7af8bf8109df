import js from '@eslint/js';
import tseslint from 'typescript-eslint';

export default [
  js.configs.recommended,
  ...tseslint.configs.recommended,
  {
    files: ['**/*.{js,mjs,cjs,ts}'],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
    },
    rules: {
      // TypeScript specific rules
      '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'warn',

      // General JavaScript/TypeScript rules
      'no-debugger': 'warn',
      'prefer-const': 'warn',
      'no-var': 'warn',

      // Code style - more lenient for existing code
      'indent': 'off', // Turn off strict indentation for now
      'quotes': 'off', // Turn off quote style for now
      'semi': 'off', // Turn off semicolon rules for now
      'comma-dangle': 'off', // Turn off comma-dangle for now
    },
  },
  {
    files: ['cypress/**/*.ts', 'src/tests/**/*.ts'],
    rules: {
      // Relax some rules for test files
      'no-console': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },
  {
    ignores: [
      'node_modules/**',
      'dist/**',
      'build/**',
      'cypress/downloads/**',
      'cypress/screenshots/**',
      'cypress/videos/**',
      '.history/**',
      'projects/**/tasks.ts',
      'eslint.config.js',
    ],
  },
];
